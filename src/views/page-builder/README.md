# YYL页面构建器

基于现有YYL组件库的可视化页面构建系统

## 目录结构

```
src/views/page-builder/
├── README.md                    # 项目说明文档
├── index.vue                   # 主入口页面
├── components/                 # 构建器组件
│   ├── Designer/              # 设计器核心组件
│   │   ├── index.vue         # 设计器主界面
│   │   ├── ComponentPanel.vue # YYL组件面板
│   │   ├── Canvas.vue        # 画布区域
│   │   ├── PropertyPanel.vue  # 属性配置面板
│   │   └── LayerPanel.vue    # 图层管理面板
│   ├── PropertyEditors/       # 属性编辑器
│   │   ├── BasicEditor.vue   # 基础属性编辑器
│   │   ├── FormFieldsEditor.vue # 表单字段编辑器
│   │   ├── TableColumnsEditor.vue # 表格列编辑器
│   │   └── PermissionEditor.vue # 权限编辑器
│   ├── RenderEngine/          # 渲染引擎
│   │   ├── ComponentRenderer.vue # 组件渲染器
│   │   └── PreviewRenderer.vue   # 预览渲染器
│   └── Common/                # 通用组件
│       ├── DragProxy.vue     # 拖拽代理组件
│       └── SelectionBox.vue  # 选择框组件
├── configs/                   # 配置文件
│   ├── yyl-components.js     # YYL组件配置
│   ├── component-templates.js # 组件模板
│   └── default-settings.js   # 默认设置
├── mixins/                    # 混入
│   ├── DragMixin.js          # 拖拽功能混入
│   ├── ComponentMixin.js     # 组件通用功能
│   └── PermissionMixin.js    # 权限处理混入
├── utils/                     # 工具函数
│   ├── componentScanner.js   # 组件扫描器
│   ├── dragUtils.js          # 拖拽工具
│   ├── codeGenerator.js      # 代码生成器
│   └── storageUtils.js       # 存储工具
├── store/                     # 状态管理
│   ├── index.js              # store入口
│   └── modules/              # store模块
│       ├── designer.js       # 设计器状态
│       ├── components.js     # 组件状态
│       └── pages.js          # 页面状态
└── styles/                    # 样式文件
    ├── designer.less         # 设计器样式
    ├── components.less       # 组件样式
    └── variables.less        # 样式变量
```

## 功能特性

- 🎨 可视化拖拽设计
- 🧩 基于现有YYL组件库
- 🔐 完整的权限控制
- 📱 响应式设计支持
- 💾 页面保存和加载
- 🔧 代码生成功能

## 开发计划

1. **阶段一**: 基础框架搭建
2. **阶段二**: YYL组件集成
3. **阶段三**: 高级功能开发
4. **阶段四**: 优化和完善

## 使用方式

访问路径: `/page-builder`

## 技术栈

- Vue 2.x
- Ant Design Vue
- Vuex
- 现有YYL组件库
