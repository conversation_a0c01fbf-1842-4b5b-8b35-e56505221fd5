<template>
  <div class="debug-page">
    <h2>页面构建器调试页面</h2>
    <p>这个页面用于测试页面构建器的菜单配置是否正确。</p>
    
    <div class="debug-info">
      <h3>当前路由信息</h3>
      <pre>{{ $route }}</pre>
      
      <h3>菜单数据</h3>
      <pre>{{ menuData }}</pre>
      
      <h3>路由数据</h3>
      <pre>{{ routerData }}</pre>
    </div>
    
    <div class="test-links">
      <h3>测试链接</h3>
      <a-button @click="goToDesigner">页面设计器</a-button>
      <a-button @click="goToPages">页面管理</a-button>
      <a-button @click="goToTemplates">模板库</a-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'PageBuilderDebug',
  computed: {
    ...mapState({
      menuData: state => state.user.menuData,
      routerData: state => state.user.routerData
    })
  },
  methods: {
    goToDesigner() {
      this.$router.push('/page-builder/components/Designer/index')
    },
    goToPages() {
      this.$router.push('/page-builder/components/PageManager/PageList')
    },
    goToTemplates() {
      this.$router.push('/page-builder/components/PageManager/TemplateGallery')
    }
  },
  mounted() {
    console.log('页面构建器调试页面已加载')
    console.log('菜单数据:', this.menuData)
    console.log('路由数据:', this.routerData)
  }
}
</script>

<style lang="less" scoped>
.debug-page {
  padding: 24px;
  
  .debug-info {
    margin: 24px 0;
    
    h3 {
      color: #1890ff;
      margin-bottom: 12px;
    }
    
    pre {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
    }
  }
  
  .test-links {
    margin: 24px 0;
    
    .ant-btn {
      margin-right: 8px;
    }
  }
}
</style>
