<template>
  <div>
    <a-form id="formLogin" class="user-layout-login" ref="formLogin" :form="form" @submit="handleSubmit">
      <a-tabs :activeKey="customActiveKey" :tabBarStyle="{ textAlign: 'center', borderBottom: 'unset', color: '#eee' }" @change="handleTabClick">
        <a-tab-pane key="tab1" tab="账号密码登录">
          <a-alert v-if="isLoginError" type="error" showIcon style="margin-bottom: 24px" message="账户或密码错误" />
          <a-form-item>
            <a-input size="large" type="text" placeholder="账户/手机号" :maxLength="11" v-decorator="[
                'username',
                {
                  rules: [{ required: true, message: '请输入账户/手机号' }, { validator: handleUsernameOrEmail }],
                  validateTrigger: 'change',
                },
              ]">
              <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
            </a-input>
          </a-form-item>

          <a-form-item>
            <a-input size="large" type="password" autocomplete="false" placeholder="密码" :maxLength="15" v-decorator="[
                'password',
                { rules: [{ required: true, message: '请输入密码' }], validateTrigger: 'blur' },
              ]">
              <a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }" />
            </a-input>
          </a-form-item>
          <a-form-item>
            <a-input size="large" type="text" autocomplete="false" placeholder="请输入验证码" :maxLength="6" v-decorator="[
                'captchaResult',
                {
                  rules: [{ required: true, message: '请输入验证码' }],
                  validateTrigger: 'blur',
                },
              ]">
              <template slot="suffix">
                <img :src="'data:image/png;base64,' + captchaImg" height="38" @click="getCaptchaImg" style="cursor: pointer" title="点击更换验证码" />
              </template>
            </a-input>
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane key="tab2" tab="手机号验证码登录">
          <a-form-item>
            <a-input size="large" type="text" placeholder="手机号" :maxLength="11" v-decorator="[
                'mobile',
                {
                  rules: [{ required: true, pattern: /^1[23456789]\d{9}$/, message: '请输入正确的手机号' }],
                  validateTrigger: 'change',
                },
              ]">
              <a-icon slot="prefix" type="mobile" :style="{ color: 'rgba(0,0,0,.25)' }" />
            </a-input>
          </a-form-item>

          <a-row :gutter="16">
            <a-col class="gutter-row" :span="16">
              <a-form-item>
                <a-input size="large" type="text" placeholder="验证码" :maxLength="6" v-decorator="[
                    'captcha',
                    { rules: [{ required: true, message: '请输入验证码' }], validateTrigger: 'blur' },
                  ]">
                  <a-icon slot="prefix" type="mail" :style="{ color: 'rgba(0,0,0,.25)' }" />
                </a-input>
              </a-form-item>
            </a-col>
            <a-col class="gutter-row" :span="8">
              <a-button class="getCaptcha" tabindex="-1" :disabled="state.smsSendBtn" @click.stop.prevent="getCaptcha" v-text="(!state.smsSendBtn && '获取验证码') || state.time + ' s'"></a-button>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>

      <!--
      <a-form-item>
        <router-link
          :to="{ name: 'recover', params: { user: 'aaa'} }"
          class="forge-password"
          style="float: right;color:#eee"
        >忘记密码？</router-link>
      </a-form-item>
      -->
      <a-form-item style="margin-top: 24px">
        <a-button size="large" type="primary" htmlType="submit" class="login-button" :loading="state.loginBtn" :disabled="state.loginBtn">确定
        </a-button>
      </a-form-item>
    </a-form>

    <two-step-captcha v-if="requiredTwoStepCaptcha" :visible="stepCaptchaVisible" @success="stepCaptchaSuccess" @cancel="stepCaptchaCancel">
    </two-step-captcha>

    <!-- 图形验证码 -->
    <CaptchaImgModal ref="iCaptchaImgModal" @ok="sendCode" />
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { timeFix } from '@/utils/util'
import { sendSMSCode, getImageCodeByAuth } from '@/api/login'
import { apiHead } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin.js'


export default {
  components: {},
  mixins: [mixinDevice],
  data() {
    return {
      customActiveKey: 'tab1',
      loginBtn: false,
      // login type: 0 email, 1 username, 2 telephone
      loginType: 0,
      isLoginError: false,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      form: this.$form.createForm(this),
      state: {
        time: 60,
        loginBtn: false,
        // login type: 0 email, 1 username, 2 telephone
        loginType: 0,
        smsSendBtn: false,
      },
      captchaTicket: '',
      captchaImg: null,
      url: {},
    }
  },
  created() {
    // this.requiredTwoStepCaptcha = true
    //pre loading admin domain
    // 图形验证码
    this.getCaptchaImg()

    // postAction(`/api_admin/User/LoginManage`, { UserName: "username" }).then((res) => { console.log(res.Data); });
  },
  methods: {
    ...mapActions(['Logout', 'Login']),
    /**
     * @description: 图形验证码
     */
    getCaptchaImg() {
      let that = this
      getImageCodeByAuth().then((res) => {
        if (res.IsSuccess) {
          that.captchaTicket = res.Data.CaptchaId
          that.captchaImg = res.Data.CaptchaBase64
        } else {
          that.$notification['error']({
            message: '提示',
            description: res.Msg,
            duration: 8,
          })
        }
      })
    },
    // handler
    handleUsernameOrEmail(rule, value, callback) {
      const { state } = this
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
      if (regex.test(value)) {
        state.loginType = 0
      } else {
        state.loginType = 1
      }
      callback()
    },
    handleTabClick(key) {
      this.customActiveKey = key
      // this.form.resetFields()
    },
    handleSubmit(e) {
      e.preventDefault()
      const {
        form: { validateFields },
        state,
        customActiveKey,
        Login,
      } = this
      state.loginBtn = true
      const validateFieldsKey =
        customActiveKey === 'tab1' ? ['username', 'password', 'captchaResult'] : ['mobile', 'captcha']
      if (this.customActiveKey === 'tab1') {
        // 用户名密码
        validateFields(validateFieldsKey, { force: true }, (err, values) => {
          if (!err) {
            const loginParams = {
              username: values.username,
              password: values.password,
              captchaId: this.captchaTicket,
              captchaResult: values.captchaResult,
            }
            Login({ loginParams, loginType: 1 })
              .then((res) => this.loginSuccess(res))
              .catch((err) => {
                this.getCaptchaImg()
                this.requestFailed(err)
              })
              .finally(() => {
                state.loginBtn = false
              })
          } else {
            state.loginBtn = false
          }
        })
      } else {
        // 手机号登录
        validateFields(validateFieldsKey, { force: true }, (err, values) => {
          if (!err) {
            const loginParams = { Phone: values.mobile, Code: values.captcha }
            Login({ loginParams, loginType: 2 })
              .then((res) => {
                if (res.IsSuccess) {
                  state.smsSendBtn = true
                  this.loginSuccess(res)
                } else {
                  state.loginBtn = false
                  this.getCaptchaImg()
                  this.$notification['error']({
                    message: '提示',
                    description: res.Msg,
                    duration: 8,
                  })
                }
              })
              .catch((err) => {
                state.loginBtn = false
                this.requestFailed(err)
              })
          } else {
            state.loginBtn = false
          }
        })
      }
    },
    getCaptcha(e) {
      e.preventDefault()
      const {
        form: { validateFields },
        state,
      } = this

      validateFields(['mobile'], { force: true }, (err, values) => {
        if (!err && this.$refs.iCaptchaImgModal) {
          this.$refs.iCaptchaImgModal.show()
        }
      })
    },
    sendCode(captchaData) {
      const { state } = this

      state.smsSendBtn = true

      const interval = window.setInterval(() => {
        if (state.time-- <= 0) {
          state.time = 60
          state.smsSendBtn = false
          window.clearInterval(interval)
        }
      }, 1000)

      const hide = this.$message.loading('验证码发送中..', 0)
      sendSMSCode({
        PhoneNumber: this.form.getFieldValue('mobile'),
        CaptchaResult: captchaData.Code,
        CaptchaId: captchaData.captchaId,
        AppId: window._CONFIG['SMSAppId'],
      })
        .then((res) => {
          setTimeout(hide, 1)
          if (res.IsSuccess) {
            this.$notification['success']({
              message: '提示',
              description: '验证码获取成功，请注意查收',
              duration: 8,
            })
          } else {
            state.time = 60
            state.smsSendBtn = false
            this.$notification['error']({
              message: '提示',
              description: res.Msg,
              duration: 8,
            })
          }
        })
        .catch((err) => {
          setTimeout(hide, 1)
          clearInterval(interval)
          state.time = 60
          state.smsSendBtn = false
          this.requestFailed(err)
        })
    },
    stepCaptchaSuccess() {
      this.loginSuccess()
    },
    stepCaptchaCancel() {
      this.Logout().then(() => {
        this.loginBtn = false
        this.stepCaptchaVisible = false
      })
    },
    validateMobile(rule, value, callback) {
      if (!value || new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(value)) {
        callback()
      } else {
        var tips = '您的手机号码格式不正确!'
        callback(tips)
      }
    },
    validateInputCode(rule, value, callback) {
      if (!value || this.verifiedCode == this.inputCodeContent) {
        //eslint-disable-line
        callback()
      } else {
        var tips = '您输入的验证码不正确!'
        callback(tips)
      }
    },
    loginSuccess(res) {
      // this.initMaoDian(res.Data.accessToken, res.Data.UserId, res.Data.NickName)
      this.state.loginBtn = false
      this.state.smsSendBtn = false
      const lastPageObj = sessionStorage.getItem('LAST_PAGE_URL')
      if (lastPageObj) {
        // 跳转到登录前的页面
        const routerObj = JSON.parse(lastPageObj)
        this.$router.push({ path: routerObj.path, query: routerObj.query })
        sessionStorage.removeItem('LAST_PAGE_URL')
      } else {
        //跳转到首页
        this.$router.push({ path: '/' })
      }
      // 延迟 1 秒显示欢迎信息
      setTimeout(() => {
        this.$notification.success({
          message: '欢迎',
          description: `${timeFix()}，欢迎回来`,
        })
      }, 1000)
      sessionStorage.setItem('login', 'new')
      sessionStorage.setItem('restStart', 'null')
      this.isLoginError = false
    },
    requestFailed(err) {
      this.isLoginError = true
      this.$notification['error']({
        message: '错误',
        description: (err || {}).Msg || '请求出现错误，请稍后再试',
        duration: 4,
      })
      if (err.ErrorCode == 67890) {
        setTimeout(() => {
          location.reload(true);
        }, 1500);
      }
    },
    initMaoDian(token, userId, userName) {
      // //初始化埋点对象
      // var ld = this._bearer(token, userId, userName)
      // console.log(ld)
      // this.Track = new Track({
      //   Root: apiHead('BURYINGPOINT'),
      //   MonitorId: ld.MonitorId,
      //   ApplicationVersion: window._CONFIG['Version'],
      //   Origin: '',
      //   OS: ld.OS,
      //   GPS: ld.location,
      //   DeviceNo: ld.deviceno,
      //   DeviceName: ld.DeviceName,
      //   BrowserInfo: ld.BrowserInfo,
      //   Authorization: ld.Authorization || '',
      // })
      // // if (ld.UserId) {
      // //     this.Track.linkWithUser(ld.UserId, ld.UserName, ld.Authorization,ld.Gps).then(res => {
      // //         this.Track.enter(this.$route.name)
      // //         window._CONFIG.CustomerKey = res.CustomerKey
      // //     })
      // // } else {
      // this.Track.linkWithNone(ld.Gps).then((res) => {
      //   this.Track.enter(this.$route.name)
      //   window._CONFIG.CustomerKey = res.CustomerKey
      // })
      // // }
    },
  },
}
</script>

<style lang="less" scoped>
.user-layout-login {
  color: #fff;
  width: 400px;
  margin: auto auto;

  label {
    font-size: 14px;
  }

  .ant-row {
    padding-bottom: 30px;
  }

  .getCaptcha {
    display: block;
    width: 100%;
    height: 40px;
  }

  .forge-password {
    font-size: 14px;
  }

  button.login-button {
    padding: 0 15px;
    font-size: 16px;
    height: 40px;
    width: 100%;
  }

  .user-login-other {
    text-align: left;
    margin-top: 24px;
    line-height: 22px;

    .item-icon {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1890ff;
      }
    }

    .register {
      float: right;
    }
  }
}

.ant-tabs-nav {
  .ant-tabs-tab {
    color: #fff;
  }
}
</style>
