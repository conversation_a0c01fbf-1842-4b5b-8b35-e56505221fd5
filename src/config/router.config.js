import { UserLayout,TabLayout,RouteView } from '@/components/layouts'

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
    {
      path: 'login',
      name: 'login',
      component: resolve => require(['@/views/user/Login'], resolve)
    }]
  },
  {
    path: '/',
    name: 'Home',
    component: resolve => require([TabLayout], resolve),
    meta: {
      title: '首页',
      keepAlive: true,
    },
    redirect: '/Home',
    children: []
  },
  {
    path: '/404',
    component: () => import('@/views/error/404')
  },
  {
    path: '/whitePages/EmptyPage',
    component: () => import('@/views/whitePages/EmptyPage')
  },
  {
    path: '/enquiry',
    component: RouteView,
    redirect: '/enquiry/enquiry',
    hidden: true,
    children: [
    {
      path: 'enquiry',
      name: 'enquiry',
      component: resolve => require(['@/views/enquiry/enquiry'], resolve)
    }]
  },
]