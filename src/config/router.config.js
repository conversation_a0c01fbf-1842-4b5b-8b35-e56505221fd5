import { RouteView, TabLayout, UserLayout } from '@/components/layouts'

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
    {
      path: 'login',
      name: 'login',
      component: resolve => require(['@/views/user/Login'], resolve)
    }]
  },
  {
    path: '/',
    name: 'Home',
    component: resolve => require([TabLayout], resolve),
    meta: {
      title: '首页',
      keepAlive: true,
    },
    redirect: '/Home',
    children: []
  },
  {
    path: '/404',
    component: () => import('@/views/error/404')
  },
  {
    path: '/whitePages/EmptyPage',
    component: () => import('@/views/whitePages/EmptyPage')
  },
  {
    path: '/enquiry',
    component: RouteView,
    redirect: '/enquiry/enquiry',
    hidden: true,
    children: [
    {
      path: 'enquiry',
      name: 'enquiry',
      component: resolve => require(['@/views/enquiry/enquiry'], resolve)
    }]
  },
  // YYL页面构建器路由
  {
    path: '/page-builder',
    name: 'PageBuilder',
    component: RouteView,
    meta: {
      title: '页面构建器',
      icon: 'build',
      permission: ['PAGE_BUILDER_ACCESS']
    },
    children: [
      {
        path: '',
        name: 'PageBuilderHome',
        component: resolve => require(['@/views/page-builder/index'], resolve),
        meta: {
          title: '页面构建器首页',
          hideInMenu: true
        }
      },
      {
        path: 'designer',
        name: 'PageDesigner',
        component: resolve => require(['@/views/page-builder/components/Designer/index'], resolve),
        meta: {
          title: '页面设计器',
          permission: ['PAGE_BUILDER_CREATE', 'PAGE_BUILDER_EDIT']
        }
      },
      {
        path: 'templates',
        name: 'PageTemplates',
        component: resolve => require(['@/views/page-builder/components/PageManager/TemplateGallery'], resolve),
        meta: {
          title: '页面模板',
          permission: ['PAGE_BUILDER_TEMPLATE_ACCESS']
        }
      },
      {
        path: 'pages',
        name: 'PageManager',
        component: resolve => require(['@/views/page-builder/components/PageManager/PageList'], resolve),
        meta: {
          title: '页面管理',
          permission: ['PAGE_BUILDER_ACCESS']
        }
      }
    ]
  },
]
