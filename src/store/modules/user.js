import Vue from 'vue'
import { login, login<PERSON>y<PERSON><PERSON>, getUserMenuList, refreshToken, getUserPermissionList } from '@/api/login'
import {
  ACCESS_TOKEN,
  USER_NAME,
  USER_INFO,
  USER_ID,
  USER_TYPE,
  USER_EXPIREDIN,
  USER_REFRESHTOKEN,
  USER_PASSWORDLEVEL
} from '@/store/mutation-types'
import { welcome } from '@/utils/util'

export default {
  name: 'user',
  state: {
    token: '', //用户token
    username: '', //用户名称
    realname: '', //真实名称
    welcome: '', //
    avatar: '', //头像
    info: {}, //登录用户model
    menuList: [], //用户的菜单数据
    buttonIds: [], //菜单配置的所有按钮的id的数据
    buttonPermissionSet: new Set(), // 菜单配置的所有按钮的权限数据
    excludePageList: [], //排除缓存的页面
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, { username, realname, welcome }) => {
      state.username = username
      state.realname = realname
      state.welcome = welcome
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_INFO: (state, info) => {
      state.info = info
    },
    SET_CurTab: (state, id) => {
      if (id) {
        state.menuList.map((x) => {
          x.isChecked = x.id == id
        })
      }
    },
    /**
     * 设置用户菜单数据
     * @param {*} state
     * @param {*} menuList
     */
    SET_MENULIST: (state, menuList) => {
      state.menuList = menuList
    },
    /**
     * 设置按钮id数据
     * @param {*} state
     * @param {*} list
     */
    SET_BUTTONIDS: (state, list) => {
      state.buttonIds = list
    },
    /**
     * 添加按钮id数据
     * @param {*} state
     * @param {*} list
     */
    ADD_BUTTONIDS: (state, list) => {
      if (list && list.length > 0) {
        state.buttonIds = state.buttonIds.concat(list)
      }
    },
    CLEAR_BUTTON_PERMISSIONS: (state) => {
      state.buttonPermissionSet.clear()
    },
    ADD_BUTTON_PERMISSIONS: (state, permissionId) => {
      state.buttonPermissionSet.add(permissionId)
    },
    // 设置排除缓存的页面
    SET_EXCLUDEPAGELIST: (state, list) => {
      if (list && list.length > 0) {
        state.excludePageList = state.excludePageList.concat(list)
      }
    }
  },
  getters: {
    getCurTabMenu: (state) => () => {
      let menu = null
      let list = state.menuList.filter((x) => {
        return x.isChecked == true
      })
      if (list && list.length > 0) {
        menu = list[0]
      }
      return menu
    },
    /**
     * 检查按钮是否有权限
     */
    checkButtonPermissions: (state) => (id) => {
      // console.log('按钮id是',id) 设置null为不显示
      if (id === undefined || id === '') {
        return true
      }
      if (!id || !state.buttonIds || state.buttonIds.length == 0) {
        return false
      }
      return state.buttonIds.includes(id)
    },
    /**
     * 检查列是否有权限
     */
    checkColumnPermissions: (state) => (key) => {
      if (!key) {
        return false
      }
      return true
    },
  },

  actions: {
    /**
     * 设置当前选中的tab
     */
    setCurTabIsChecked({ commit }, id) {
      commit('SET_CurTab', id)
    },
    /**
     * 登录
     */
    Login({ commit }, { loginParams, loginType }) {
      let loginRequest = null
      if (loginType == 2) {
        loginRequest = loginByPhone(loginParams)
      } else {
        loginRequest = login(loginParams)
      }
      if (!loginRequest) {
        console.log('无登录类型,登录失败')
        reject(error)
      }
      return new Promise((resolve, reject) => {
        loginRequest
          .then((response) => {
            loginSuccess(response, resolve, reject, (result) => {
              Vue.ls.set(USER_NAME, result.LoginName)
              Vue.ls.set(USER_INFO, result)
              Vue.ls.set(USER_ID, result.UserId)
              Vue.ls.set(USER_TYPE, result.UserType)
              Vue.ls.set(USER_PASSWORDLEVEL, result.PasswordLevel)
              commit('SET_TOKEN', result.AccessToken)
              commit('SET_INFO', result)
              commit('SET_NAME', {
                username: result.LoginName,
                realname: result.Name,
                welcome: welcome(),
              })
              commit('SET_AVATAR', result.HeaderImgUrl)
            })
          })
          .catch((error) => {
            console.log('登录错误：' + error)
            reject(error)
          })
      })
    },

    /**
     * 通过RefreshToken获取新的访问令牌token
     * */

    GetRefreshToken({ commit }) {
      return new Promise((resolve, reject) => {
        let getRefreshToken = Vue.ls.get(USER_REFRESHTOKEN)
        if (!getRefreshToken) {
          reject('失败啦！')
        }
        let params = {
          refreshToken: getRefreshToken,
        }
        refreshToken(params)
          .then((response) => {
            loginSuccess(response, resolve, reject, null)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    /**
     * 获取用户菜单Menu数据----有自己的菜单权限管理的系统使用此方法
     * */
    loadUserMenuData({ commit }) {
      return new Promise((resolve, reject) => {
        let userId = Vue.ls.get(USER_ID)
        if (!userId) {
          reject('无用户id！')
        }
        let params = {
          // token: v_token,
          appid: window._CONFIG['SystemAppId'],
          user: userId,
          noMenuId: true,
        }
        getUserMenuList(params)
          .then((res) => {
            if (!res.IsSuccess || !res.Data) {
              resolve({
                success: false,
                message: res.Msg,
                code: 200,
                result: null,
              })
            } else {
              // 菜单数据重组
              if (res.Data.length > 0) {
                commit('SET_BUTTONIDS', [])
                commit('CLEAR_BUTTON_PERMISSIONS')
                // 获取最终的数据
                let processType = process.env.VUE_APP_PREVIEW.trim()
                if (processType === 'test') {
                  let testObj = setTestObj()
                  res.Data[0].children.push(testObj)
                }
                let endMenu = setMenuData(commit, res.Data[0].children, 1)
                // 过滤按钮
                outBtnFilter(endMenu, 3)

                res.Data = {
                  applicationId: '',
                  applicationName: res.Data[0].Name || window._CONFIG['systemName'],
                  menus: endMenu,
                }
              }

              var response = {
                success: true,
                message: '查询成功',
                code: 200,
                result: {
                  menu: [],
                },
              }
              //移除菜单路径上的参数
              removeMenuParams(res.Data.menus)
              response.result.menu = changeMenu(res.Data.menus)
              const menuData = response.result.menu
              if (menuData && menuData.length > 0) {
                commit('SET_MENULIST', menuData)
              } else {
                reject('当前用户无任何菜单权限')
              }

              resolve(response)
            }
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    /**
     * 获取用户菜单Menu数据-权限管理走应用管理平台的系统使用此方法
     * */
    loadPermissionList({ commit }) {
      return new Promise((resolve, reject) => {
        // let v_token = Vue.ls.get(ACCESS_TOKEN);
        let userId = Vue.ls.get(USER_ID)
        if (!userId) {
          reject('登录失败！')
        }
        let params = {
          // token: v_token,
          appid: window._CONFIG['SystemAppId'],
          user: userId,
        }
        getUserPermissionList(params)
          .then((res) => {
            if (!res.IsSuccess || !res.Data) {
              resolve({
                success: false,
                message: res.Msg,
                code: 200,
                result: null,
              })
            } else {
              var response = {
                success: true,
                message: '查询成功',
                code: 200,
                result: {
                  menu: [],
                },
              }
              //移除菜单路径上的参数
              removeMenuParams(res.Data.menus)
              response.result.menu = changeMenu(res.Data.menus)
              const menuData = response.result.menu
              if (menuData && menuData.length > 0) {
                commit('SET_MENULIST', menuData)
              } else {
                reject('当前用户无任何菜单权限')
              }
              resolve(response)
            }
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    // 登出
    Logout({ commit, state }) {
      return new Promise((resolve) => {
        let logoutToken = state.token
        commit('SET_TOKEN', '')
        commit('SET_MENULIST', [])
        Vue.ls.remove(ACCESS_TOKEN)
        Vue.ls.remove(USER_NAME)
        Vue.ls.remove(USER_ID)
        Vue.ls.remove(USER_INFO)
        Vue.ls.remove(USER_EXPIREDIN)
        Vue.ls.remove(USER_REFRESHTOKEN)
        Vue.ls.remove(USER_TYPE)
        Vue.ls.remove(USER_PASSWORDLEVEL)
        sessionStorage.removeItem('AREA_TREE_DATA')
        if (!logoutToken) {
          resolve()
        } else {
          resolve()
          // logout(logoutToken).then(() => {
          //   resolve()
          // }).catch(() => {
          //   resolve()
          // })
        }
      })
    },
  },
}

function setTestObj() {
  const localTest = location.href.indexOf('localhost:') > -1 || location.href.indexOf('192.168.') > -1 // 验证是否本地启动
  let obj = {
    IsVerifyData: false,
    Name: "测试(开发用)",
    Path: "pages/test",
    Sort: localTest ? 12 : 99,
    // Sort: 12,//设置99就隐藏菜单
    Type: 1,
    TypeStr: "文件夹",
    Icon:'info-circle',
    children: [
      {
        IsCheck: false,
        IsVerifyData: false,
        Name: "测试",
        Path: "pages/test/testPage1",
        Sort: 1,
        Type: 2,
        TypeStr: "页面",
      },
      {
        IsCheck: false,
        IsVerifyData: false,
        Name: "测试2",
        Path: "pages/test/testPage2",
        Sort: 1,
        Type: 2,
        TypeStr: "页面",
      },
      {
        IsCheck: false,
        IsVerifyData: false,
        Name: "测试3",
        Path: "pages/test/testPage3",
        Sort: 1,
        Type: 2,
        TypeStr: "页面",
      }
    ]
  }
  return obj
}

// 递归重组菜单数据
var setMenuData = function (commit, orgMenu, level) {
  if (orgMenu == null || orgMenu.length <= 0) {
    return []
  }
  let tempArr = []
  let ids = []
  let excludePageList = []
  try {
    orgMenu.forEach((item) => {
      //类型是按钮的话，id存起来
      if (item.Type == 3) {
        ids.push(item.Id)
        commit('ADD_BUTTON_PERMISSIONS', item.Id)
      }
      var temp = {
        aliasName: '',
        applicationId: '',
        icon: item.Icon,
        id: item.Id,
        name: item.Name,
        parentId: item.ParentId,
        sort: item.Sort,
        url: item.Path,
        type: item.Type,
        level: level,
      }
      // 过滤详情的数据不缓存
      if (item.Sort == 99) {
        let lastSignIndex = item.Path.lastIndexOf('/') > -1 ? item.Path.lastIndexOf('/') : null
        let path = item.Path
        if (lastSignIndex != null) {
          path = item.Path.substring(lastSignIndex + 1, item.Path.length)
        } else {
          path = item.Path
        }
        if (path) {
          excludePageList.push(path)
        }
      }

      if (item.children && item.children.length > 0) {
        temp['children'] = setMenuData(commit, item.children, level + 1)
      }

      tempArr.push(temp)
    })
    commit('ADD_BUTTONIDS', ids)
    commit('SET_EXCLUDEPAGELIST', excludePageList)
  } catch (e) {
    console.log(e)
  }
  return tempArr
}

// 过滤按钮
var outBtnFilter = function (arr, value) {
  return arr.filter((item) => {
    if (item.type === value) {
      return false
    }
    if (item.children && item.children.length > 0) {
      item.children = outBtnFilter(item.children, value)
    }
    return true
  })
}
/***
 * 登录成功 保存一些数据
 */
var loginSuccess = function (response, resolve, reject, callback) {
  if (response.IsSuccess && response.Data) {
    const result = response.Data
    Vue.ls.set(ACCESS_TOKEN, result.AccessToken, result.ExpiredIn * 1000)
    Vue.ls.set(USER_EXPIREDIN, result.ExpiredIn, result.ExpiredIn * 1000)
    Vue.ls.set(USER_REFRESHTOKEN, result.RefreshToken, result.ExpiredIn * 1000)
    callback && callback(result)
    resolve(response)
  } else {
    reject(response)
  }
}
/**
 * 接口菜单数据转换为路由数据
 * @param {*} orgMenu
 * @returns
 */
var changeMenu = function (orgMenu) {
  if (orgMenu == null || orgMenu.length <= 0) {
    return []
  }
  let tempArr = []
  try {
    orgMenu.forEach((item) => {
      let index = item.url.indexOf('/')
      if (index == 0) {
        item.component = item.url.substring(index + 1, item.url.length)
        item.path = item.url
      } else {
        item.component = item.url
        item.path = '/' + item.url
      }
      var temp = {
        redirect: null,
        path: item.path,
        component: item.children && item.children.length ? 'layouts/RouteView' : item.component,
        sort: item.sort,
        level: item.level,
        meta: {
          keepAlive: true,
          icon: item.icon,
          title: item.name,
          url: 'MENUID' + item.id, //此url之前没使用未赋值 为null
        },
        name: item.children && item.children.length ? 'layouts/RouteView' + item.path : item.component,
        id: item.id,
        // actions:
        //   item.children && item.children.length
        //     ? item.children.filter(x => {
        //         x.Type == 3
        //       })
        //     : []
      }
      if (item.configParams) {
        temp.meta.configParams = item.configParams
      }
      //这个地方增加参数会在地址中显示出来
      // else {
      //   temp.meta.configParams = {
      //     menuId: item.id
      //   }
      // }

      temp['hidden'] = item.sort != 99 && item.sort != 98 ? false : true
      if (item.children && item.children.length > 0) {
        temp['children'] = changeMenu(item.children)
      }
      tempArr.push(temp)
    })
  } catch (e) {
    console.log(e)
  }
  return tempArr
}
/**
 * 移除菜单路径上的参数
 * @param {*} menus
 */
var removeMenuParams = function (menus) {
  for (const i in menus) {
    let index = menus[i].url.indexOf('?')
    if (index > -1) {
      menus[i].configParams = urlToJson(menus[i].url, menus[i].name)
      if (menus[i].url) {
        menus[i].url = menus[i].url.split('?')[0]
      }
      if (menus[i].component) {
        menus[i].component = menus[i].component.split('?')[0]
      }
      if (menus[i].path) {
        menus[i].path = menus[i].path.split('?')[0]
      }
    }
    if (menus[i].children && menus[i].children.length) {
      removeMenuParams(menus[i].children)
    }
  }
}
var urlToJson = (url, title) => {
  let obj = {},
    index = url.indexOf('?'), // 看url有没有参数
    params = url.substr(index + 1) // 截取url参数部分
  if (index != -1) {
    // 有参数时
    let parr = params.split('&') // 将参数分割成数组
    for (let i of parr) {
      // 遍历数组
      let arr = i.split('=')
      obj[arr[0]] = arr[1]
    }
    obj.title = title
  }
  return obj
}