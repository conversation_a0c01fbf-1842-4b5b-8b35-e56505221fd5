// import api from './index'
// import { axios } from '@/utils/request'、
// import Vue from 'vue'
import { getAction,postAction } from './manage'
//登录流程api配置
const url = {
  loginByPassword: '/{v}/Login/PasswordLogin', //密码登录
  loginByPhone: '/{v}/Login/VerifyCodeLogin', //验证码登录
  loginByToken: '', //token登录
  sendSMSCode: '/api/Sms/SendSMSVerifyCode', //发送验证码
  imageCodeByAuth: '/api/GenCaptcha', //图形验证码-认证中心
  imageCodeBySMS: '/api/Captcha/GenCaptcha', //图形验证码-短信中心
  resetPwd: '/{v}/UserPermission/User/ResetPasswordByCode', //重置密码
  getMenu: '/{v}/UserPermission/Menu/ListByToken', //获取菜单
  refreshToken: '/{v}/Login/RefreshToken', //刷新token- 对应方法未使用
}
/**
 * 密码登录
 * @param {*} parameter
 * @returns
 */
export function login(parameter) {
  // console.log('登录JS==>login')
  return getAction(url.loginByPassword, parameter || {}, 'BASE')
}
/**
 * 手机验证码登录
 * @param {*} parameter
 * @returns
 */
export function loginByPhone(parameter) {
  // console.log('登录JS==>loginByPhone')
  return getAction(url.loginByPhone, parameter || {}, 'BASE')
}
/**
 * 重置密码
 * @param {*} parameter
 * @returns
 */
export function resetPassword(parameter) {
  // console.log('登录JS==>resetPassword')
  return postAction(url.resetPwd, parameter || {}, 'BASE')
}
/**
 * 获取登录用户菜单
 * @param {*} parameter
 * @returns
 */
export function getUserMenuList(parameter) {
  // console.log('登录JS==>getUserMenuList')
  return getAction(url.getMenu, parameter || {}, 'BASE')
}
/**
 * 刷新token
 * @param {*} parameter
 * @returns
 */
export function refreshToken(parameter) {
  // console.log('登录JS==>refreshToken')
  return getAction(url.refreshToken, parameter || {}, 'BASE')
}

/**
 * 发送验证码
 * @param {*} parameter
 * @returns
 */
export function sendSMSCode(parameter) {
  // console.log('登录JS==>sendSMSCode')
  return getAction(url.sendSMSCode, parameter || {}, 'SMS')
}
/**
 * 获取图形验证码-认证中心
 * @param {*} parameter
 * @returns
 */
export function getImageCodeByAuth(parameter) {
  // console.log('登录JS==>getImageCodeByAuth')
  return getAction(url.imageCodeByAuth, parameter || {}, 'CODELOGIN')
}
/**
 * 获取图形验证码-短信中心
 * @param {*} parameter
 * @returns
 */
export function getImageCodeBySMS(parameter) {
  // console.log('登录JS==>getImageCodeBySMS')
  return getAction(url.imageCodeBySMS, parameter || {}, 'SMS')
}
/**
 * 获取用户菜单-走应用配置中心数据
 * @param {*} parameter
 * @returns
 */
export function getUserPermissionList(parameter) {
  // console.log('登录JS==>getUserPermissionList')
  return getAction('/api/privileges/user_app_menus', params, 'PERMISSION')
}

/**
 * login func
 * @param parameter 通过 token 直接登录 暂未使用
 * @returns {*}
 */
// export function loginByToken(parameter, token) {
//   return axios({
//     url: `${Vue.API.P36001}${api.LoginByToken}`,
//     method: 'get',
//     data: parameter,
//     headers: {
//       Authorization: 'Bearer ' + token,
//     },
//   })
// }

