{"name": "vue-antd-jeecg", "version": "2.0.1", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve-stage": "vue-cli-service serve --mode stage", "serve-pro": "vue-cli-service serve --mode production-build", "build-test": "vue-cli-service build --mode production-test", "stage": "vue-cli-service build --mode stage", "build-pro": "vue-cli-service build --mode production-build", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@antv/data-set": "^0.10.1", "@tinymce/tinymce-vue": "^3.2.2", "ant-design-vue": "^1.7.8", "axios": "^0.18.0", "clipboard": "^2.0.4", "core-js": "^2.6.12", "echarts": "^5.4.3", "font-awesome": "^4.7.0", "js-cookie": "^2.2.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "mathjs": "^11.12.0", "md5": "^2.2.1", "moment": "^2.24.0", "nprogress": "^0.2.0", "pdfjs-dist": "2.5.207", "pdfjs-dist-sign": "2.5.207", "qrcodejs2": "^0.0.2", "tinymce": "^5.3.2", "v-viewer": "^1.7.4", "vue": "^2.5.22", "vue-class-component": "^6.0.0", "vue-cropper": "^0.4.8", "vue-html5-editor": "^1.1.1", "vue-i18n": "^8.7.0", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-pdf-signature": "^4.2.7", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.7", "vue-property-decorator": "^7.3.0", "vue-router": "^3.0.1", "vue-splitpane": "^1.0.4", "vuedraggable": "^2.20.0", "vuex": "^3.0.1", "vuex-class": "^0.3.1", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "@babel/eslint-parser": "^7.12.16", "eslint": "^5.12.0", "eslint-plugin-vue": "^5.1.0", "less": "^3.8.1", "less-loader": "^4.1.0", "sass": "^1.32.7", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.5.22"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-console": 0}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}